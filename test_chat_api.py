#!/usr/bin/env python3
"""
Test script for Chat API
Tests the chat functionality and displays responses properly
"""

import requests
import json
import time

# API Configuration
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/v1/login"
CHAT_URL = f"{BASE_URL}/api/v1/chat"

# Credentials
USERNAME = "admin"
PASSWORD = "admin123"
CLIENT_ID = "ambition-guru"

def login():
    """Login and get access token"""
    print("🔐 Logging in...")
    
    login_data = {
        "username": USERNAME,
        "password": PASSWORD,
        "client_id": CLIENT_ID
    }
    
    response = requests.post(LOGIN_URL, data=login_data)
    
    if response.status_code == 200:
        data = response.json()
        token = data.get("access_token")
        print(f"✅ Login successful! Token: {token[:50]}...")
        return token
    else:
        print(f"❌ Login failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def chat(token, message):
    """Send chat message and get response"""
    print(f"\n💬 Sending message: {message}")
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    chat_data = {
        "message": message
    }
    
    start_time = time.time()
    response = requests.post(CHAT_URL, headers=headers, json=chat_data)
    end_time = time.time()
    
    print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Chat successful!")
        print(f"📝 Response: {data.get('response', 'No response')}")
        print(f"🧵 Thread ID: {data.get('thread_id', 'No thread ID')}")
        print(f"👤 User ID: {data.get('user_id', 'No user ID')}")
        
        # Check for tools used in different possible formats
        tools_used = data.get('tools_used', [])
        tool_calls = data.get('tool_calls', [])

        if tools_used:
            print(f"🛠️  Tools used: {len(tools_used)}")
            for i, tool in enumerate(tools_used, 1):
                print(f"   {i}. {tool.get('name', 'Unknown')} - {tool.get('description', 'No description')}")
        elif tool_calls:
            print(f"🛠️  Tool calls: {len(tool_calls)}")
            for i, tool in enumerate(tool_calls, 1):
                print(f"   {i}. {tool.get('tool', 'Unknown')} - {tool.get('input', 'No input')}")
        else:
            print("🛠️  No tools used")
            # Print full response to debug
            print(f"🔍 Full response keys: {list(data.keys())}")
            
        return data
    else:
        print(f"❌ Chat failed: {response.status_code}")
        print(f"Response: {response.text}")
        return None

def test_conversation():
    """Test a full conversation flow"""
    print("🚀 Starting Chat API Test")
    print("=" * 50)
    
    # Login
    token = login()
    if not token:
        return
    
    # Test messages
    messages = [
        "Hello, I want to know about SEE Bridge course",
        "I want to book the SEE Bridge course. My name is John Doe and my <NAME_EMAIL>",
        "My phone number is +977-9841234567",
        "What other courses do you offer?",
        "Tell me about IELTS preparation course"
    ]
    
    print(f"\n🎯 Testing {len(messages)} messages...")
    
    for i, message in enumerate(messages, 1):
        print(f"\n{'='*20} Message {i}/{len(messages)} {'='*20}")
        response = chat(token, message)
        
        if not response:
            print(f"❌ Failed at message {i}, stopping test")
            break
            
        # Wait a bit between messages
        if i < len(messages):
            print("⏳ Waiting 2 seconds before next message...")
            time.sleep(2)
    
    print(f"\n{'='*50}")
    print("✅ Chat API Test Completed!")

if __name__ == "__main__":
    test_conversation()
