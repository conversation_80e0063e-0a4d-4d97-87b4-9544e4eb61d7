#!/usr/bin/env python3

import requests
import json

def get_auth_token():
    """Get authentication token"""
    login_url = "http://localhost:8000/api/v1/login"
    
    # Try with test credentials
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            print(f"Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Login request failed: {e}")
        return None

def test_chat_with_auth():
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Could not get auth token")
        return
    
    print(f"✅ Got auth token: {token[:20]}...")
    
    url = "http://localhost:8000/api/v1/chat"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # Test messages
    test_messages = [
        "Hi",
        "What courses do you have?", 
        "I want to book a course",
        "Yes"
    ]
    
    for message in test_messages:
        print(f"\n🗣️ USER: {message}")
        
        payload = {
            "message": message
        }
        
        try:
            response = requests.post(url, json=payload, headers=headers, timeout=None)
            
            if response.status_code == 200:
                data = response.json()
                print(f"🤖 AGENT: {data.get('response', 'No response')}")
                
                tools_used = data.get('tools_used', [])
                if tools_used:
                    print(f"🔧 TOOLS: {[tool['name'] for tool in tools_used]}")
                    
            else:
                print(f"❌ Error: {response.status_code} - {response.text}")
                break
                
        except Exception as e:
            print(f"❌ Request failed: {e}")
            break

if __name__ == "__main__":
    test_chat_with_auth()
