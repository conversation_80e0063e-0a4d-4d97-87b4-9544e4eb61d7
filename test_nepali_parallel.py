#!/usr/bin/env python3
"""
Test script for Nepali language support and parallel tool execution
"""

import asyncio
import requests
import json
import time

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test_user_nepali"

def test_chat_endpoint(message, user_id=TEST_USER_ID):
    """Test the chat endpoint with a message"""
    url = f"{BASE_URL}/api/v1/chat"
    
    payload = {
        "message": message,
        "user_id": user_id
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"\n🔄 Testing message: '{message}'")
        print("=" * 50)
        
        start_time = time.time()
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        end_time = time.time()
        
        print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"🤖 Response: {result.get('response', 'No response')}")
            
            tools_used = result.get('tools_used', [])
            if tools_used:
                print(f"🛠️  Tools Used: {', '.join(tools_used)}")
            else:
                print("🛠️  Tools Used: None")
                
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Run comprehensive tests"""
    print("🧪 Testing Enhanced Agent with Nepali Support and Parallel Execution")
    print("=" * 70)
    
    # Test cases
    test_cases = [
        # Basic greetings in different languages
        {
            "message": "नमस्ते",
            "description": "Nepali greeting"
        },
        {
            "message": "Hello",
            "description": "English greeting"
        },
        {
            "message": "नमस्ते, कस्तो छ?",
            "description": "Nepali greeting with question"
        },
        
        # Course inquiries in Nepali
        {
            "message": "कुन कोर्स छ?",
            "description": "What courses are available? (Nepali)"
        },
        {
            "message": "SEE Bridge कोर्स के हो?",
            "description": "Mixed language course inquiry"
        },
        
        # Booking requests in Nepali
        {
            "message": "कोर्स बुक गर्न चाहन्छु",
            "description": "I want to book a course (Nepali)"
        },
        {
            "message": "IELTS लिन्छु",
            "description": "I'll take IELTS (Nepali)"
        },
        
        # Complex queries that should trigger parallel tools
        {
            "message": "What courses do you have and how much do they cost?",
            "description": "Complex query requiring multiple tools"
        },
        {
            "message": "कुन कोर्स छ र कसरी बुक गर्ने?",
            "description": "Complex Nepali query requiring multiple tools"
        },
        
        # Help requests in Nepali
        {
            "message": "मद्दत चाहिन्छ",
            "description": "Need help (Nepali)"
        }
    ]
    
    # Run tests
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test {i}/{total}: {test_case['description']}")
        success = test_chat_endpoint(test_case['message'])
        if success:
            passed += 1
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All tests passed! Nepali support and parallel execution working correctly.")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the logs for details.")
    
    print("\n🔍 Key Features Tested:")
    print("  ✓ Nepali language understanding")
    print("  ✓ Mixed language support")
    print("  ✓ Cultural greetings (Namaste)")
    print("  ✓ Parallel tool execution for complex queries")
    print("  ✓ Natural conversation flow")

if __name__ == "__main__":
    main()
