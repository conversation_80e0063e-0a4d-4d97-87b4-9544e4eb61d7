"""
Global Cache Manager for Application Components
Provides startup caching for agents, tools, and vector stores to improve performance
"""

import logging
from typing import Dict, Optional, Any
from threading import Lock
from models.user import UserTenantDB
from api.services.chat_service import ChatService
from api.services.agent_v2.main_agent import MainAgentV2
from api.services.dynamic_tool_manager import DynamicToolManager, ToolConfig
from utils.production_memory_manager import get_production_memory_manager
from config import get_vector_store_manager

logger = logging.getLogger(__name__)


class GlobalCacheManager:
    """
    Global cache manager for application components
    Caches agents, tools, and vector stores per tenant for performance
    """
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._lock = Lock()
        self._initialized_tenants: set = set()
        
    def _get_tenant_cache(self, tenant_id: str) -> Dict[str, Any]:
        """Get or create cache for a specific tenant"""
        if tenant_id not in self._cache:
            self._cache[tenant_id] = {
                'vector_store_manager': None,
                'production_memory': None,
                'main_agent': None,
                'tool_manager': None,
                'tools': None,
                'chat_service': None
            }
        return self._cache[tenant_id]
    
    def initialize_tenant_cache(self, tenant_id: str) -> None:
        """Initialize cache for a specific tenant at startup"""
        with self._lock:
            if tenant_id in self._initialized_tenants:
                return
                
            logger.info(f"🚀 Initializing cache for tenant: {tenant_id}")
            
            try:
                cache = self._get_tenant_cache(tenant_id)
                
                # Initialize vector store manager
                cache['vector_store_manager'] = get_vector_store_manager(tenant_id)
                logger.info(f"✅ Vector store manager cached for tenant: {tenant_id}")
                
                # Initialize production memory with OpenAI to match vector store embeddings
                cache['production_memory'] = get_production_memory_manager(tenant_id, "openai")
                logger.info(f"✅ Production memory cached for tenant: {tenant_id}")
                
                self._initialized_tenants.add(tenant_id)
                logger.info(f"🎯 Tenant cache initialized successfully: {tenant_id}")
                
            except Exception as e:
                logger.error(f"❌ Failed to initialize cache for tenant {tenant_id}: {e}")
                # Remove from initialized set so it can be retried
                self._initialized_tenants.discard(tenant_id)
    
    def get_or_create_chat_service(self, current_user: UserTenantDB, tool_config: ToolConfig = None) -> ChatService:
        """Get cached chat service or create new one for user"""
        tenant_id = current_user.tenant_id
        
        # Ensure tenant cache is initialized
        if tenant_id not in self._initialized_tenants:
            self.initialize_tenant_cache(tenant_id)
        
        cache = self._get_tenant_cache(tenant_id)
        
        # For chat service, we create per-user instances but reuse cached components
        # This is because chat service needs user-specific context
        chat_service = ChatService(current_user, tool_config)
        
        # Inject cached components to avoid re-initialization
        if cache['vector_store_manager']:
            current_user._vector_store_manager = cache['vector_store_manager']
        
        return chat_service
    
    def get_cached_vector_store_manager(self, tenant_id: str):
        """Get cached vector store manager"""
        if tenant_id not in self._initialized_tenants:
            self.initialize_tenant_cache(tenant_id)
        
        cache = self._get_tenant_cache(tenant_id)
        return cache['vector_store_manager']
    
    def get_cached_production_memory(self, tenant_id: str):
        """Get cached production memory manager"""
        if tenant_id not in self._initialized_tenants:
            self.initialize_tenant_cache(tenant_id)
        
        cache = self._get_tenant_cache(tenant_id)
        return cache['production_memory']
    
    def warm_up_tenant(self, tenant_id: str) -> None:
        """Warm up all components for a tenant"""
        logger.info(f"🔥 Warming up tenant: {tenant_id}")
        
        # Initialize basic cache
        self.initialize_tenant_cache(tenant_id)
        
        # Pre-load vector stores by accessing them
        try:
            cache = self._get_tenant_cache(tenant_id)
            if cache['vector_store_manager']:
                # Access retrievers to ensure they're loaded
                cache['vector_store_manager'].get_info_retriever()
                cache['vector_store_manager'].get_product_retriever()
                logger.info(f"✅ Vector stores warmed up for tenant: {tenant_id}")
        except Exception as e:
            logger.warning(f"⚠️ Could not warm up vector stores for tenant {tenant_id}: {e}")
    
    def clear_tenant_cache(self, tenant_id: str) -> None:
        """Clear cache for a specific tenant"""
        with self._lock:
            if tenant_id in self._cache:
                del self._cache[tenant_id]
            self._initialized_tenants.discard(tenant_id)
            logger.info(f"🗑️ Cleared cache for tenant: {tenant_id}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'initialized_tenants': len(self._initialized_tenants),
            'cached_tenants': list(self._initialized_tenants),
            'total_cache_entries': len(self._cache)
        }


# Global cache manager instance
cache_manager = GlobalCacheManager()


def get_cache_manager() -> GlobalCacheManager:
    """Get the global cache manager instance"""
    return cache_manager


def initialize_default_tenants():
    """Initialize cache for default/known tenants at startup"""
    default_tenants = [
        "686a02179e8eee0d9f2c5db6",  # ambition-guru tenant
        # Add other known tenant IDs here
    ]
    
    for tenant_id in default_tenants:
        try:
            cache_manager.warm_up_tenant(tenant_id)
        except Exception as e:
            logger.warning(f"⚠️ Could not warm up tenant {tenant_id}: {e}")
