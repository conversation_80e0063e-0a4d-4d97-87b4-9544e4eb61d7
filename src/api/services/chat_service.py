"""
Chat Service - Encapsulates agents, tools, and vector DB for dynamic tenant-based chat
Only initializes when needed to reduce unnecessary resource allocation
"""

import logging
from typing import Optional
from models.user import UserTenantDB
from api.services.agent_v2.main_agent import MainAgentV2
from api.services.agent_v2.search_agent import SearchAgentV2
from api.services.agent_v2.simple_booking_agent_new import SimpleBookingAgent
from api.services.dynamic_tool_manager import D<PERSON>ToolManager, ToolConfig, create_dynamic_tool_manager
# Removed unused imports - using logger directly

logger = logging.getLogger(__name__)


class ChatService:
    """
    Chat service that provides access to agents, tools, and vector DB
    Initialized with current user context for dynamic tenant support
    """

    def __init__(self, current_user: UserTenantDB, tool_config: ToolConfig = None):
        """
        Initialize chat service with current user context

        Args:
            current_user: Current user with tenant info and vector store access
            tool_config: Configuration for which tools to enable (optional)
        """
        self.current_user = current_user
        self.tenant_id = current_user.tenant_id
        self.vector_store_manager = current_user.vector_store_manager  # Lazy initialization

        # Initialize dynamic tool manager
        self.tool_manager = DynamicToolManager(current_user, tool_config)

        # Initialize agents with current user context
        self._main_agent: Optional[MainAgentV2] = None
        self._search_agent: Optional[SearchAgentV2] = None
        self._booking_agent: Optional[SimpleBookingAgent] = None

        # Lazy tool creation - only create when needed
        self._tools: Optional[list] = None

        # Reduced logging noise - only log essential info
        logger.info(f"ChatService initialized for tenant: {self.tenant_id}")
        logger.debug(f"Tool configuration: {self.tool_manager.get_tool_descriptions()}")

    def _create_tools(self):
        """Create tools using dynamic tool manager"""
        return self.tool_manager.create_tools()

    @property
    def tools(self):
        """Get or create tools with lazy initialization"""
        if self._tools is None:
            logger.debug("Creating tools with current user context...")
            self._tools = self._create_tools()
            logger.debug("Tools created successfully")
        return self._tools

    @property
    def main_agent(self) -> MainAgentV2:
        """Get or initialize main agent with current user context"""
        if self._main_agent is None:
            logger.debug("Initializing Main Agent with current user context...")
            self._main_agent = MainAgentV2(current_user=self.current_user)
            self._main_agent.set_tools(self.tools)  # Set tools with current user context
            logger.info("✅ Main Agent initialized successfully with tools")
        return self._main_agent
    
    @property
    def search_agent(self) -> SearchAgentV2:
        """Get or initialize search agent with current user context"""
        if self._search_agent is None:
            logger.debug(f"Initializing Search Agent for tenant: {self.tenant_id}")
            self._search_agent = SearchAgentV2(tenant_id=self.tenant_id)
            logger.info("✅ Search Agent initialized successfully")
        return self._search_agent
    
    @property
    def booking_agent(self) -> SimpleBookingAgent:
        """Get or initialize booking agent with current user context"""
        if self._booking_agent is None:
            logger.debug("Initializing Booking Agent with current user context...")
            self._booking_agent = SimpleBookingAgent(current_user=self.current_user)
            logger.info("✅ Booking Agent initialized successfully")
        return self._booking_agent
    
    def chat(self, message: str, thread_id: str = None) -> dict:
        """
        Process chat message using the main agent

        Args:
            message: User's message
            thread_id: Optional thread ID (defaults to user ID)

        Returns:
            Dict with response and tools_used
        """
        # Use user ID as thread ID if not provided
        if thread_id is None:
            thread_id = str(self.current_user.user.id)

        logger.debug(f"Processing chat for user {self.current_user.user.username} (tenant: {self.tenant_id})")

        # Use main agent to process the message
        return self.main_agent.chat(message, thread_id)

    async def chat_stream(self, message: str, thread_id: str = None):
        """
        Process chat message using the main agent with streaming response

        Args:
            message: User's message
            thread_id: Optional thread ID (defaults to user ID)

        Yields:
            Dict chunks with streaming response and tools_used
        """
        # Use user ID as thread ID if not provided
        if thread_id is None:
            thread_id = str(self.current_user.user.id)

        logger.debug(f"Processing streaming chat for user {self.current_user.user.username} (tenant: {self.tenant_id})")

        # Use main agent to process the message with streaming
        async for chunk in self.main_agent.chat_stream(message, thread_id):
            yield chunk
    
    
    def search_products(self, query: str) -> str:
        """Search for products using current user's vector store"""
        return self.vector_store_manager.search_products(query)
    
    def search_information(self, query: str) -> str:
        """Search for information using current user's vector store"""
        return self.vector_store_manager.search_information(query)
    
    def handle_booking(self, user_message: str, thread_id: str = None) -> str:
        """Handle booking request using current user context"""
        if thread_id is None:
            thread_id = str(self.current_user.user.id)

        return self.booking_agent.handle_booking_request(user_message, thread_id)

    def update_tool_config(self, new_config: ToolConfig):
        """Update tool configuration and recreate tools"""
        self.tool_manager.update_config(new_config)
        # Clear cached tools to force recreation with new config
        self._tools = None
        # Clear cached main agent to force recreation with new tools
        self._main_agent = None
        logger.info(f"🔄 Updated tool configuration for tenant {self.tenant_id}")

    def get_available_tools(self) -> dict:
        """Get list of currently available tools"""
        return self.tool_manager.get_tool_descriptions()

    def set_tools_for_session(self, config_type: str):
        """Set tools for current session based on predefined configuration"""
        config_map = {
            "default": DynamicToolManager.get_default_config(),
            "search_only": DynamicToolManager.get_search_only_config(),
            "info_only": DynamicToolManager.get_info_only_config(),
            "products_only": DynamicToolManager.get_products_only_config()
        }

        if config_type in config_map:
            self.update_tool_config(config_map[config_type])
            logger.info(f"🛠️ Set tools to '{config_type}' configuration")
        else:
            logger.warning(f"⚠️ Unknown configuration type: {config_type}")

    def enable_tool(self, tool_name: str):
        """Enable a specific tool"""
        current_config = self.tool_manager.tool_config

        if tool_name == "search_information":
            current_config.search_information = True
        elif tool_name == "search_products":
            current_config.search_products = True
        elif tool_name == "handle_booking":
            current_config.handle_booking = True

        self.update_tool_config(current_config)
        logger.info(f"✅ Enabled tool: {tool_name}")

    def disable_tool(self, tool_name: str):
        """Disable a specific tool"""
        current_config = self.tool_manager.tool_config

        if tool_name == "search_information":
            current_config.search_information = False
        elif tool_name == "search_products":
            current_config.search_products = False
        elif tool_name == "handle_booking":
            current_config.handle_booking = False

        self.update_tool_config(current_config)
        logger.info(f"❌ Disabled tool: {tool_name}")


def create_chat_service(current_user: UserTenantDB,
                       tool_config: ToolConfig = None,
                       config_type: str = "default") -> ChatService:
    """
    Factory function to create chat service with current user context

    Args:
        current_user: Current user with tenant info
        tool_config: Custom tool configuration (optional)
        config_type: Predefined configuration type if tool_config not provided

    Returns:
        ChatService instance configured for the user's tenant
    """
    if tool_config is None:
        # Use predefined configuration
        tool_manager = create_dynamic_tool_manager(current_user, config_type)
        return ChatService(current_user, tool_manager.tool_config)
    else:
        # Use custom configuration
        return ChatService(current_user, tool_config)
