"""
Simple Booking Agent - Clean booking agent for ChatService
"""

import logging
from typing import List, Dict
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langgraph.checkpoint.mongodb import MongoDBSaver
from models.user import UserTenantDB
from core.database import get_db_from_tenant_id
from api.services.booking_service import BookingService
from utils.production_memory_manager import get_production_memory_manager
import os
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Note: Using shared MongoDB memory instead of separate sessions


class SimpleBookingAgent:
    """
    Simple Booking Agent - Clean booking workflow
    """
    
    def __init__(self, current_user : UserTenantDB=None):
        """Initialize the booking agent"""
        self.current_user = current_user

        # LLM for context understanding
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )

        # Access to search tools through current user
        self.vector_store_manager = current_user.vector_store_manager if current_user else None

        # Initialize booking service for database operations
        if current_user and current_user.tenant_id:
            self.booking_service = BookingService(current_user.tenant_id)
        else:
            self.booking_service = None

        # Initialize production memory system for personalization
        if current_user and current_user.tenant_id:
            self.production_memory = get_production_memory_manager(current_user.tenant_id, "gemini")
        else:
            self.production_memory = None

        # Use the same MongoDB memory as main agent
        if current_user and current_user.tenant_id:
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)
            mongo_client = tenant_db.client
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )
            logger.info(f"✅ Booking agent using shared MongoDB memory for tenant: {current_user.tenant_id}")
        else:
            self.memory = None
            logger.warning("⚠️ No memory available for booking agent")

        logger.info("✅ Simple Booking Agent initialized")

    def handle_booking_request(self, user_message: str, thread_id: str) -> str:
        """Handle booking request"""
        try:
            logger.info(f"📅 Booking request: {user_message}")

            # Get conversation context from shared memory
            conversation_context = self._get_conversation_context(thread_id)
            logger.info(f"🔍 Booking agent conversation context available")

            # Check if user already has a selected product
            selected_product = None
            search_results = ""

            if self.production_memory:
                # Use simple confirmed product fetch instead of complex search
                try:
                    confirmed_product = self.production_memory.get_confirmed_product(str(self.current_user.user.id))
                    if confirmed_product:
                        selected_product = {
                            "name": confirmed_product["name"],
                            "code": confirmed_product["code"]
                        }
                        logger.info(f"✅ Found confirmed product: {confirmed_product['name']} ({confirmed_product['code']})")
                except Exception as e:
                    logger.warning(f"Could not get confirmed product from production memory: {e}")
                    selected_product = None

            # Only search for products if no product is selected yet
            if not selected_product and self.vector_store_manager:
                try:
                    # Get the raw search documents first
                    retriever = self.vector_store_manager.get_product_retriever()
                    if retriever:
                        search_docs = retriever.invoke(user_message)

                        # Check if user is selecting from the search results
                        self._check_for_course_selection(user_message, search_docs, thread_id)

                        # Now get the formatted search results
                        search_results = self.vector_store_manager.search_products(user_message)
                        logger.info(f"🔍 Found course search results for booking request")
                    else:
                        search_results = "No courses available at the moment."

                except Exception as e:
                    logger.warning(f"⚠️ Could not search for courses: {e}")
            elif selected_product:
                logger.info(f"✅ Using previously selected product: {selected_product['name']} ({selected_product['code']})")
                search_results = f"Selected Course: {selected_product['name']} (Code: {selected_product['code']})"

            # Get personalized context using production memory
            personalized_context = "No personalization available."
            if self.production_memory:
                try:
                    user_id = str(self.current_user.user.id)
                    personalized_context = self.production_memory.get_user_context(user_id, user_message)
                    logger.info(f"✅ Got personalized context from production memory")
                except Exception as e:
                    logger.warning(f"⚠️ Could not get context from production memory: {e}")



            # Use simplified LLM prompt for faster processing
            if selected_product:
                system_prompt = """You are a booking assistant. Help complete the course booking.

LANGUAGE: Understand Nepali/English. "hajur", "chu", "huncha" = "yes/okay".

TASK: User selected a course. Help them book it:
1. Confirm course selection
2. Check profile - don't re-ask for existing info (name/email/phone)
3. Get missing details if needed
4. Schedule booking

Be brief and efficient.

Student Profile:
{personalized_context}

Selected Course:
{search_context}

Conversation History Context:
{conversation_context}

Focus on completing the booking for their selected course. If all user information is available in the profile, proceed directly to scheduling."""
            else:
                system_prompt = """You are a helpful booking assistant for an educational service center in Nepal.

🌐 LANGUAGE SUPPORT:
- You can understand and respond to messages in Nepali, English, or mixed languages
- When users write in Nepali (नेपाली), understand their intent naturally and respond in English or Romanized Nepali
- Be culturally appropriate and use "Namaste" when greeting Nepali speakers
- CRITICAL NEPALI EXPRESSIONS TO UNDERSTAND:
  * "हजुर" / "hajur" = "Yes" / "Okay" / "I agree"
  * "छ" / "chu" = "Yes" / "Okay" / "Alright"
  * "हुन्छ" / "huncha" = "Okay" / "That's fine"
  * "गर्छु" / "garchu" = "I will do" / "I agree"
  * "चाहिन्छ" / "chahicha" = "I want" / "I need"
- Examples: "कुन कोर्स चाहिन्छ?" = "Which course do you need?", "समय कहिले?" = "What time?"
- When user says "hajur", "chu", "huncha" etc., treat it as positive confirmation/agreement

Help users book courses by:
1. Understanding what course they want to book (use search results if available)
2. Using the student profile to provide personalized recommendations
3. Once they select a course, remember their choice and proceed with booking
4. IMPORTANT: Check the Student Profile first - if name, email, and phone are already available, DO NOT ask for them again. Only ask for missing information.
5. Offering available time slots
6. Confirming the booking and saving it to database

GREETING BEHAVIOR:
🆕 FOR NEW CUSTOMERS (when profile shows "NEW CUSTOMER"):
- Welcome them warmly to the booking process (use "Namaste" for Nepali speakers)
- Ask what course they'd like to book
- Be encouraging and helpful

🔄 FOR RETURNING CUSTOMERS (when profile shows customer name):
- Use their name in greeting with appropriate cultural greeting
- Reference their previous interests if available
- Help them select from available courses

Be friendly and guide them through the process step by step.
Use "Namaste" as greeting when appropriate, especially for Nepali speakers.

CRITICAL RULE: NEVER ask for information that is already provided in the Student Profile. If the profile shows the user's name, email, and phone number, use that information directly.

Student Profile:
{personalized_context}

Available Courses:
{search_context}

Conversation History Context:
{conversation_context}

Respond helpfully to their request with personalized recommendations based on their profile."""

            search_context = f"Available courses from search:\n{search_results}\n" if search_results else "No specific course search results available."

            response = self.llm.invoke([
                SystemMessage(content=system_prompt.format(
                    personalized_context=personalized_context,
                    search_context=search_context,
                    conversation_context=conversation_context
                )),
                HumanMessage(content=f"User message: {user_message}")
            ])

            # Product selection is handled in _check_for_course_selection above

            result = response.content

            # Track incomplete process if booking was started but not completed
            self._track_booking_progress(user_message, result, thread_id)

            logger.info(f"✅ Booking response generated")
            return result
            
        except Exception as e:
            error_msg = f"Error handling booking: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble processing your booking request. Please try again or contact our support team."

    def _get_conversation_context(self, thread_id: str) -> str:
        """Get conversation context from shared MongoDB memory"""
        if not self.memory:
            return "No conversation history available."

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Return recent conversation context for the agent to use
                context_info = []
                for msg in messages[-10:]:  # Last 10 messages for context
                    if hasattr(msg, 'content'):
                        context_info.append(f"Message: {msg.content}")

                return "\n".join(context_info) if context_info else "No conversation history found."
            else:
                return "No conversation history found."

        except Exception as e:
            logger.warning(f"Could not retrieve conversation context: {e}")
            return "Error retrieving conversation history."

    def _get_conversation_messages(self, thread_id: str) -> List[Dict[str, str]]:
        """Get conversation messages in format suitable for memory system"""
        if not self.memory:
            return []

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Convert to memory system format
                conversation_messages = []
                for msg in messages[-20:]:  # Last 20 messages for better context
                    if hasattr(msg, 'content') and msg.content:
                        # Determine role based on message type
                        if hasattr(msg, 'type'):
                            if msg.type == 'human':
                                role = 'user'
                            elif msg.type == 'ai':
                                role = 'assistant'
                            else:
                                role = 'user'  # Default to user
                        else:
                            role = 'user'  # Default to user

                        conversation_messages.append({
                            "role": role,
                            "content": msg.content
                        })

                return conversation_messages
            else:
                return []

        except Exception as e:
            logger.warning(f"Could not retrieve conversation messages: {e}")
            return []

    def _check_for_course_selection(self, user_message: str, search_results: str, _thread_id: str) -> None:
        """Use simple keyword detection for course selection"""
        try:
            user_msg_lower = user_message.lower()

            # Simple confirmation keywords
            confirmation_keywords = ["yes", "okay", "sure", "hajur", "chu", "huncha", "book", "want", "take"]

            if any(keyword in user_msg_lower for keyword in confirmation_keywords):
                # Try to extract course from search results using simple text matching
                if "SEE Bridge" in search_results and "SEE-BRIDGE" in search_results:
                    course_name, course_code = "SEE Bridge Course", "SEE-BRIDGE"
                elif "IELTS" in search_results and "IELTS-PREP" in search_results:
                    course_name, course_code = "IELTS Preparation", "IELTS-PREP"
                elif "BBS" in search_results and "BBS-SEM1" in search_results:
                    course_name, course_code = "BBS 1st Semester", "BBS-SEM1"
                elif "BBA" in search_results and "BBA-SEM1" in search_results:
                    course_name, course_code = "BBA 1st Semester", "BBA-SEM1"
                else:
                    return  # No clear course identified

                # Save the confirmed product to production memory
                if self.production_memory:
                    try:
                        user_id = str(self.current_user.user.id)
                        self.production_memory.save_confirmed_product(user_id, course_name, course_code)
                        logger.info(f"🎯 User confirmed course: {course_name} ({course_code})")
                    except Exception as e:
                        logger.warning(f"Could not save confirmed product to production memory: {e}")

        except Exception as e:
            logger.warning(f"⚠️ Could not check course selection: {e}")

    def _track_booking_progress(self, user_message: str, agent_response: str, thread_id: str) -> None:
        """Track booking progress and identify incomplete processes"""
        try:
            if not self.production_memory:
                return

            user_id = str(self.current_user.user.id)

            # Use simple keyword detection instead of LLM calls for better performance
            user_msg_lower = user_message.lower()
            response_lower = agent_response.lower()

            # Simple keyword-based detection
            booking_keywords = ["book", "enroll", "register", "sign up", "want", "interested"]
            incomplete_keywords = ["need", "provide", "details", "information", "contact"]

            booking_started = any(keyword in user_msg_lower for keyword in booking_keywords)
            booking_incomplete = any(keyword in response_lower for keyword in incomplete_keywords)

            if booking_started and booking_incomplete:
                # Extract course name from the conversation
                course_name = self._extract_course_from_conversation(user_message, agent_response)

                # Track the incomplete process
                process_details = f"Booking process started for {course_name}" if course_name else "Course booking process started"
                next_steps = "Collect user details and confirm booking"

                # Use the track_incomplete_process tool if available
                memory_tools = self.production_memory.create_memory_tools(self.current_user)
                track_tool = None
                for tool in memory_tools:
                    if hasattr(tool, 'name') and tool.name == 'track_incomplete_process':
                        track_tool = tool
                        break

                if track_tool:
                    try:
                        track_tool.invoke({
                            "process_type": "booking",
                            "process_details": process_details,
                            "next_steps": next_steps
                        })
                        logger.info(f"📝 Tracked incomplete booking process: {process_details}")
                    except Exception as e:
                        logger.warning(f"Could not track incomplete process: {e}")

        except Exception as e:
            logger.warning(f"Error tracking booking progress: {e}")

    def _extract_course_from_conversation(self, user_message: str, agent_response: str) -> str:
        """Extract course name from conversation context using simple text processing"""
        try:
            combined_text = f"{user_message} {agent_response}".lower()

            # Simple keyword matching for common courses
            course_keywords = {
                "see bridge": "SEE Bridge Course",
                "ielts": "IELTS Preparation",
                "bbs": "BBS Program",
                "bba": "BBA Program",
                "csit": "CSIT Program",
                "german": "German Language",
                "korean": "Korean Language"
            }

            for keyword, course_name in course_keywords.items():
                if keyword in combined_text:
                    return course_name

            return "Course"

        except Exception as e:
            logger.warning(f"Error extracting course from conversation: {e}")
            return "Unknown Course"

    def get_pending_booking_reminder(self, _thread_id: str) -> str:
        """Get reminder message for pending booking"""
        # Since we're using shared memory, no separate booking reminders needed
        return ""
