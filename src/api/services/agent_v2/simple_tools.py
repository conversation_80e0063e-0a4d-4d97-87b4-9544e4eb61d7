"""
Simple Tools - No hardcoding, delegate to existing systems
"""

import logging
from langchain_core.tools import tool
from models.user import UserTenantDB

logger = logging.getLogger(__name__)


def create_simple_tools(current_user: UserTenantDB):
    """Create simple tools that delegate to existing systems"""
    
    @tool
    def search_information(query: str) -> str:
        """Search for general information, help, and answers to questions"""
        try:
            if current_user and current_user.vector_store_manager:
                result = current_user.vector_store_manager.search_information(query)
                logger.info(f"✅ Information search completed for: {query}")
                return result
            else:
                return "I can help you with information about our courses and services. What would you like to know?"
        except Exception as e:
            logger.error(f"Information search failed: {e}")
            return "I'm here to help with any questions you have about our educational services."

    @tool  
    def search_products(query: str) -> str:
        """Search for courses, programs, and educational products"""
        try:
            if current_user and current_user.vector_store_manager:
                result = current_user.vector_store_manager.search_products(query)
                logger.info(f"✅ Product search completed for: {query}")
                return result
            else:
                return "We offer various educational courses including SEE Bridge, IELTS, BBS, BBA, and CSIT programs. What specific course interests you?"
        except Exception as e:
            logger.error(f"Product search failed: {e}")
            return "We have many educational programs available. Let me know what you're interested in learning."

    @tool
    def handle_booking(user_message: str) -> str:
        """Handle course booking and enrollment requests"""
        try:
            logger.info(f"📅 Booking request: {user_message}")
            
            # Simple booking response - let the agent handle the conversation flow
            user_msg_lower = user_message.lower()
            
            # Check for confirmation words
            confirmations = ["yes", "okay", "sure", "hajur", "chu", "huncha", "book", "enroll"]
            if any(word in user_msg_lower for word in confirmations):
                return "Great! I'll help you with the booking. To proceed, I'll need your name, email, and phone number. What's your full name?"
            
            # Check for course mentions
            if any(course in user_msg_lower for course in ["see", "bridge", "ielts", "bbs", "bba", "csit"]):
                return "Perfect! I can help you book that course. Would you like to proceed with the enrollment?"
            
            # General booking response
            return "I'd be happy to help you with course booking! Which course would you like to enroll in? We have SEE Bridge, IELTS, BBS, BBA, and CSIT programs available."
            
        except Exception as e:
            logger.error(f"Booking handling failed: {e}")
            return "I'll help you with booking. Which course would you like to enroll in?"

    return [search_information, search_products, handle_booking]
