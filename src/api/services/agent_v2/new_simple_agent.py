"""
New Simple Agent - Clean implementation with no extra LLM calls or hardcoding
Uses only main agent <PERSON><PERSON> with tools and long-term memory
"""

import logging
from datetime import datetime
from typing import Dict, List

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain.agents import create_react_agent, AgentExecutor
from langchain_core.prompts import Chat<PERSON>romptTemplate

from models.user import UserTenantDB
from utils.production_memory_manager import get_production_memory_manager
import os
from dotenv import load_dotenv

load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Simple agent prompt
SIMPLE_AGENT_PROMPT = """You are a helpful assistant for Ambition Guru Education.

LANGUAGE: Understand Nepali/English. "hajur", "chu", "huncha" = "yes/okay".

TOOLS:
- search_information: For general questions and help
- search_products: For course/program questions  
- handle_booking: For booking and enrollment

BEHAVIOR:
- Use tools when needed
- Be brief and helpful
- Remember user context: {personalized_context}

Respond naturally and use tools to help users."""


class NewSimpleAgent:
    """
    Simple Agent - One LLM, tools, and memory
    """
    
    def __init__(self, current_user: UserTenantDB = None):
        """Initialize the agent"""
        self.current_user = current_user
        
        # Single LLM instance
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        
        # Initialize memory
        if current_user and current_user.tenant_id:
            self.memory = get_production_memory_manager(current_user.tenant_id, "gemini")
        else:
            self.memory = None
            
        self.tools = []
        self.agent = None
        
        logger.info("✅ New Simple Agent initialized")

    def set_tools(self, tools):
        """Set tools and create agent"""
        self.tools = tools
        
        # Create simple prompt
        prompt = ChatPromptTemplate.from_messages([
            ("system", SIMPLE_AGENT_PROMPT),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}"),
        ])
        
        # Create React agent
        react_agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        # Create executor with minimal settings
        self.agent = AgentExecutor(
            agent=react_agent,
            tools=self.tools,
            verbose=False,
            handle_parsing_errors=True,
            max_iterations=2,  # Keep it simple
            max_execution_time=15,  # Fast responses
            return_intermediate_steps=False
        )
        
        logger.info("✅ Simple Agent created with tools")

    def _get_user_context(self) -> str:
        """Get user context from memory"""
        if not self.memory or not self.current_user:
            return "No user context available."
            
        try:
            user_id = str(self.current_user.user.id)
            context = self.memory.get_user_context(user_id)
            return context if context else "New user."
        except Exception as e:
            logger.warning(f"Could not get user context: {e}")
            return "No user context available."

    def chat(self, message: str, thread_id: str = None) -> dict:
        """Process chat message"""
        try:
            # Get user context
            user_context = self._get_user_context()
            
            # Update prompt with context
            formatted_prompt = SIMPLE_AGENT_PROMPT.format(personalized_context=user_context)
            
            # Create dynamic prompt
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", formatted_prompt),
                ("human", "{input}"),
                ("placeholder", "{agent_scratchpad}"),
            ])
            
            # Update agent prompt
            self.agent.agent.runnable = create_react_agent(self.llm, self.tools, dynamic_prompt)
            
            # Invoke agent
            response = self.agent.invoke({"input": message})
            
            # Save to memory
            if self.memory and self.current_user:
                try:
                    user_id = str(self.current_user.user.id)
                    chat_history = self.memory.get_chat_history(user_id)
                    chat_history.add_user_message(message)
                    chat_history.add_ai_message(response.get("output", ""))
                except Exception as e:
                    logger.warning(f"Could not save to memory: {e}")
            
            # Extract tools used
            tools_used = []
            if "intermediate_steps" in response:
                for step in response["intermediate_steps"]:
                    if hasattr(step, 'tool') and hasattr(step, 'tool_input'):
                        tools_used.append({
                            'name': step.tool,
                            'description': f'Used {step.tool}'
                        })
            
            return {
                "response": response.get("output", "I'm here to help!"),
                "tools_used": tools_used
            }
            
        except Exception as e:
            logger.error(f"Error in chat: {e}")
            return {
                "response": "I'm here to help! How can I assist you today?",
                "tools_used": []
            }

    async def chat_stream(self, message: str, thread_id: str = None):
        """Stream chat response"""
        try:
            # Get user context
            user_context = self._get_user_context()
            
            # Simple streaming - just return the regular response in chunks
            result = self.chat(message, thread_id)
            
            # Yield response in chunks
            response_text = result["response"]
            chunk_size = 50
            
            for i in range(0, len(response_text), chunk_size):
                chunk = response_text[i:i + chunk_size]
                yield {
                    "type": "chunk",
                    "content": chunk,
                    "full_response": response_text[:i + len(chunk)]
                }
            
            # Final response
            yield {
                "type": "final",
                "content": "",
                "full_response": response_text,
                "tools_used": result["tools_used"]
            }
            
        except Exception as e:
            logger.error(f"Error in streaming: {e}")
            yield {
                "type": "final",
                "content": "I'm here to help!",
                "full_response": "I'm here to help!",
                "tools_used": []
            }
