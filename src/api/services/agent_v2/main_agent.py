"""
Main Agent V2 - Modern LangChain implementation using LangGraph
Uses LangGraph's built-in persistence and memory management patterns
"""

import os
import logging
from datetime import datetime

from dotenv import load_dotenv

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate

from langgraph.checkpoint.mongodb import MongoDBSaver

from core.database import get_db_from_tenant_id
from utils import log_user_input, log_agent_response, log_chat_exchange, log_user_message, log_final_response, log_tool_call_clean
from utils.production_memory_manager import get_production_memory_manager

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)


MAIN_AGENT_PROMPT = """You are a friendly, conversational sales representative for Ambition Guru Education. You help customers naturally and only use tools when specifically needed.

🌐 LANGUAGE SUPPORT:
- You can understand and respond to messages in Nepali, English, or mixed languages
- When users write in Nepali (नेपाली), understand their intent naturally and respond in English or Romanized Nepali
- CRITICAL NEPALI EXPRESSIONS TO UNDERSTAND:
  * "हजुर" / "hajur" = "Yes" / "Okay" / "I agree"
  * "छ" / "chu" = "Yes" / "Okay" / "Alright"
  * "हुन्छ" / "huncha" = "Okay" / "That's fine"
  * "गर्छु" / "garchu" = "I will do" / "I agree"
  * "चाहिन्छ" / "chahicha" = "I want" / "I need"
  * "नमस्ते" = "Namaste", "कस्तो छ?" = "How are you?", "कोर्स के छ?" = "What courses are available?"
- Be culturally appropriate and use "Namaste" when greeting Nepali speakers
- When user says "hajur", "chu", "huncha" etc., treat it as positive confirmation/agreement

🚨 CRITICAL: DO NOT use tools for simple greetings, small talk, or general conversation. Respond naturally first.

🗣️ RESPOND NATURALLY WITHOUT TOOLS FOR:
- Greetings: "Hi", "Hello", "Hey", "Good morning", "नमस्ते", "Namaste" (BUT check for incomplete processes first!)
- Small talk: "How are you?", "What's up?", "Nice to meet you", "कस्तो छ?"
- General conversation: "Thank you", "Okay", "I see", "धन्यवाद"
- Nepali affirmations: "हजुर", "hajur", "छ", "chu", "हुन्छ", "huncha" (treat as agreement/confirmation)
- Follow-up questions about what you just said

🚨 CRITICAL GREETING BEHAVIOR:
When user greets you (Hi, Hello, नमस्ते, etc.), ALWAYS check the user context for incomplete processes:
- If incomplete processes are detected, proactively mention them and offer to continue
- Example: "Namaste Diwas! I see you were interested in booking the SEE Bridge Course last time. Would you like to continue with that booking?"
- Don't just say generic greetings - be helpful and contextual!

� CRITICAL AFFIRMATION BEHAVIOR:
When user gives Nepali affirmations ("hajur", "chu", "huncha", etc.) in response to your questions:
- Treat these as "YES" or positive confirmation
- If you asked about continuing a booking and they say "hajur", proceed with the booking process
- If you offered something and they say "chu", take it as acceptance
- Example: You: "Would you like to continue booking?" User: "hajur" → Proceed with booking immediately

CRITICAL: STOP ASKING ABOUT INCOMPLETE PROCESSES ONCE USER CONFIRMS:
- If you mentioned an incomplete booking and user says "yes", "okay", "sure", "hajur", "chu", "huncha", "ok lets book it" → USE handle_booking tool immediately
- DO NOT keep asking the same question repeatedly
- DO NOT get stuck in a loop asking about incomplete processes
- Once user confirms, take action with the appropriate tool
- SIMPLE CONFIRMATIONS LIKE "yes" MEAN PROCEED WITH THE BOOKING!
�📋 ONLY USE TOOLS WHEN USER SPECIFICALLY:
1. **search_products** - Asks "what courses do you have?", "show me courses", "कुन कोर्स छ?", "what programs are available?"
2. **search_information** - Asks for help, company info, policies, troubleshooting, "मद्दत चाहिन्छ"
3. **handle_booking** - Says "I want to book", "enroll me", "register for course", "बुक गर्न चाहन्छु"
   OR when they confirm booking with "yes", "okay", "sure", "hajur", "chu", "huncha" after you offered to continue a booking

� PARALLEL TOOL EXECUTION:
- When user asks complex questions that might need multiple types of information, you can use multiple tools
- Example: "What courses do you have and how do I book them?" → Use both search_products AND search_information
- The system will execute tools in parallel for better performance
- Always provide comprehensive responses by combining results from multiple tools when appropriate

�🗣️ NATURAL CONVERSATION EXAMPLES:

User: "Hi" / "नमस्ते"
You: "Namaste! Welcome to Ambition Guru Education. How can I help you today?"
[NO TOOLS NEEDED]

User: "Hello" / "हेलो"
You: "Hello! I'm here to help you with any questions about our educational programs. What brings you here today?"
[NO TOOLS NEEDED]

User: "How are you?" / "कस्तो छ?"
You: "I'm doing great, thank you for asking! I'm here to help you find the perfect course for your goals. What are you interested in learning?"
[NO TOOLS NEEDED]

User: "What courses do you have?" / "कुन कोर्स छ?"
You: "Let me show you our available courses!" [THEN USE search_products]

User: "I want to book a course" / "कोर्स बुक गर्न चाहन्छु"
You: "I'd be happy to help you with that!" [THEN USE handle_booking]

User: "What courses do you have and how much do they cost?"
You: "Let me get you information about our courses and pricing!" [USE search_products AND search_information in parallel]

CRITICAL AFFIRMATION EXAMPLES:
You: "I see you were interested in booking the SEE Bridge Course. Would you like to continue?"
User: "yes" / "okay" / "sure" / "hajur" / "हजुर" / "chu" / "छ"
You: [USE handle_booking immediately - don't ask again]

You: "Would you like me to show you our available courses?"
User: "huncha" / "हुन्छ" / "garchu" / "गर्छु"
You: [USE search_products immediately]

🎨 RESPONSE STYLE:
- Be warm, friendly, and conversational
- Build rapport naturally
- Ask follow-up questions to understand needs
- Only suggest tools/courses when user shows interest
- Use natural language, not robotic responses
- When responding to Nepali speakers, use culturally appropriate greetings like "Namaste"
- Respond in English or Romanized Nepali for clarity

🔧 TOOL EXECUTION INTELLIGENCE:
- Analyze user queries to determine if multiple tools are needed
- Execute tools in parallel when possible for better performance
- Combine results from multiple tools into coherent responses
- Share information between tools when one tool's result can help another

PERSONALIZATION CONTEXT:
{personalized_context}

Remember: You're a human sales rep having a natural conversation. You understand multiple languages naturally and can use multiple tools efficiently to provide comprehensive help. Don't jump to tools unless the user specifically needs information or wants to take action."""


class MainAgentV2:
    """
    Main Agent V2 - Clean implementation that works with ChatService
    Tools are provided by ChatService with current user context
    """

    def __init__(self, current_user=None):
        """Initialize the agent with current user context"""
        self.current_user = current_user

        # Use MongoDB checkpointer for persistent memory instead of in-memory MemorySaver
        if current_user and current_user.tenant_id:
            # Get tenant-specific database for memory persistence
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)

            # Create MongoDB client from the database connection
            mongo_client = tenant_db.client

            # Use official MongoDB checkpointer
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )

            # Initialize production memory system for persistent user sessions and profiles
            self.production_memory = get_production_memory_manager(current_user.tenant_id, "openai")

            logger.info(f"✅ MongoDB memory and memory system initialized for tenant: {current_user.tenant_id}")
        else:
            # Fallback to in-memory for cases without user context
            raise Exception("Tenant ID is required for MongoDB memory")

        self.llm = main_llm
        self.tools = []  # Tools will be set by ChatService
        self.agent = None  # Agent will be created when tools are set

        logger.info("✅ Main Agent V2 initialized")

    def set_tools(self, tools):
        """Set tools and create the agent"""
        self.tools = tools

        # Create the agent with tools using modern tool calling agent
        from langchain.agents import create_tool_calling_agent, AgentExecutor
        from langchain_core.prompts import ChatPromptTemplate

        # Create prompt template that will be dynamically updated with user context
        # Note: personalized_context will be injected dynamically during each conversation
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", MAIN_AGENT_PROMPT),  # Will be formatted with context during chat
            ("placeholder", "{chat_history}"),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}"),
        ])

        # Create tool calling agent (more modern than ReAct)
        agent = create_tool_calling_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=self.prompt_template
        )

        # Wrap in AgentExecutor for execution with parallel tool support
        self.agent = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=10,  # Increased for booking flow completion
            return_intermediate_steps=True,  # Enable intermediate step tracking
            max_execution_time=60  # Increased timeout for booking completion
        )
        logger.info("✅ Agent created with tools")

    def _get_personalized_context(self) -> str:
        """Get personalized context for the user"""
        if not self.current_user or not hasattr(self, 'production_memory'):
            return "No user profile available."

        try:
            user_id = str(self.current_user.user.id)

            # Get production memory context
            if self.production_memory:
                production_context = self.production_memory.get_user_context(user_id)

                context = f"""USER CONTEXT:
{production_context}

IMPORTANT: This user has a permanent session. Use save_user_memory, search_user_memories, and update_user_profile tools to manage user information."""
                return context

            return "No user profile available."
        except Exception as e:
            logger.warning(f"Could not get personalized context: {e}")
            return "No user profile available."

    def _get_conversation_messages(self, thread_id: str) -> list:
        """Get conversation messages for memory system"""
        if not self.memory:
            return []

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Convert to memory system format, filtering out tool-related messages
                conversation_messages = []
                for msg in messages[-20:]:  # Last 20 messages for better context
                    if hasattr(msg, 'content') and msg.content:
                        # Skip tool messages and messages with tool calls for memory system
                        if hasattr(msg, 'type'):
                            if msg.type == 'tool':
                                continue  # Skip tool result messages
                            elif msg.type == 'ai' and hasattr(msg, 'tool_calls') and msg.tool_calls:
                                continue  # Skip AI messages with tool calls
                            elif msg.type == 'human':
                                role = 'user'
                            elif msg.type == 'ai':
                                role = 'assistant'
                            else:
                                role = 'user'  # Default to user
                        else:
                            role = 'user'  # Default to user

                        conversation_messages.append({
                            "role": role,
                            "content": msg.content
                        })

                return conversation_messages
            else:
                return []

        except Exception as e:
            logger.warning(f"Could not retrieve conversation messages: {e}")
            return []

    def _clean_conversation_history(self, thread_id: str) -> None:
        """Clean conversation history to remove incomplete tool calls"""
        if not self.memory:
            return

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if not checkpoint:
                return

            # Get messages from checkpoint
            if isinstance(checkpoint, dict):
                messages = checkpoint.get("channel_values", {}).get("messages", [])
            elif hasattr(checkpoint, 'channel_values'):
                messages = checkpoint.channel_values.get("messages", [])
            else:
                return

            # Check for incomplete tool calls
            tool_call_ids = set()
            tool_result_ids = set()

            # First pass: collect all tool call IDs and tool result IDs
            for msg in messages:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_call_ids.add(tool_call.get('id'))

                if hasattr(msg, 'tool_call_id'):
                    tool_result_ids.add(msg.tool_call_id)

            # Find tool calls without results
            incomplete_tool_calls = tool_call_ids - tool_result_ids

            if incomplete_tool_calls:
                logger.warning(f"Found {len(incomplete_tool_calls)} incomplete tool calls, cleaning history")
                # For now, we'll let LangGraph handle this naturally
                # The error suggests the issue is with the LLM provider validation
                # We could implement more sophisticated cleaning here if needed

        except Exception as e:
            logger.warning(f"Could not clean conversation history: {e}")

    def _get_tool_description(self, tool_name: str) -> str:
        """Get detailed description of what each tool does"""
        descriptions = {
            'search_products': 'Searched for courses, programs, and educational products',
            'search_information': 'Searched for general information and troubleshooting help',
            'handle_booking': 'Processed booking request and managed enrollment workflow'
        }
        return descriptions.get(tool_name, f'Used {tool_name}')

    def _add_booking_reminder_if_needed(self, response: str, user_message: str, thread_id: str) -> str:
        """Add booking reminder if user has pending booking and is asking about other topics"""
        # Only check for reminders if user is NOT currently talking about booking
        booking_keywords = ['book', 'booking', 'enroll', 'register', 'sign up', 'course enrollment']
        is_booking_related = any(keyword in user_message.lower() for keyword in booking_keywords)

        if not is_booking_related and self.current_user:
            try:
                # Skip booking reminder for now to prevent hanging
                # TODO: Fix booking reminder implementation
                logger.debug("Skipping booking reminder check to prevent hanging")
                pass
            except Exception as e:
                logger.warning(f"Could not check for pending bookings: {e}")

        return response

    def chat(self, message: str, thread_id: str = None) -> dict:
        """
        Process a user message and return the response using LangGraph

        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management (uses permanent session if None)

        Returns:
            Dict with response and tools_used
        """
        user_id = str(self.current_user.user.id)

        # Use permanent session if no thread_id provided
        if thread_id is None:
            if hasattr(self, 'production_memory') and self.production_memory:
                session = self.production_memory.get_permanent_session(user_id)
                thread_id = session.thread_id
            else:
                thread_id = f"permanent_{user_id}"
        if not self.agent:
            raise ValueError("Agent not initialized. Call set_tools() first.")

        # Validate message content
        if not message or not message.strip():
            return {
                "response": "I didn't receive any message. Please tell me how I can help you today!",
                "tools_used": []
            }

        # Limit message length to prevent API issues
        if len(message) > 2000:
            return {
                "response": "Your message is too long. Please keep it under 2000 characters and try again.",
                "tools_used": []
            }

        log_user_input(message)

        # Store message in chat history
        if hasattr(self, 'production_memory') and self.production_memory:
            chat_history = self.production_memory.get_chat_history(user_id)
            chat_history.add_user_message(message)

        # Configure thread for memory persistence with user_id for memory tools
        config = {
            "configurable": {
                "thread_id": thread_id,
                "user_id": user_id,
                "checkpoint_ns": ""  # Add checkpoint namespace for MongoDB saver
            }
        }

        try:
            # Debug: Check if there's existing conversation history in production memory
            try:
                if hasattr(self, 'production_memory') and self.production_memory:
                    user_id = str(self.current_user.user.id)
                    chat_history_obj = self.production_memory.get_chat_history(user_id)
                    if hasattr(chat_history_obj, 'messages') and len(chat_history_obj.messages) > 0:
                        logger.info(f"🔍 Found existing conversation history for thread {thread_id}")
                        logger.info(f"📝 Previous conversation has {len(chat_history_obj.messages)} messages")
                    else:
                        logger.info(f"🆕 Starting new conversation for thread {thread_id}")
                else:
                    logger.info(f"🆕 Starting new conversation for thread {thread_id} (no production memory)")
            except Exception as e:
                logger.warning(f"⚠️ Could not check conversation history: {e}")
                logger.info(f"🆕 Starting new conversation for thread {thread_id}")

            # Production memory system handles user context automatically through tools

            # Get chat history from production memory system (which has the actual conversation)
            chat_history = []
            if hasattr(self, 'production_memory') and self.production_memory:
                try:
                    user_id = str(self.current_user.user.id)
                    chat_history_obj = self.production_memory.get_chat_history(user_id)

                    # Get the actual messages from the chat history
                    if hasattr(chat_history_obj, 'messages'):
                        # Convert LangChain messages to the format expected by AgentExecutor
                        from langchain_core.messages import HumanMessage, AIMessage
                        chat_history = []
                        for msg in chat_history_obj.messages:
                            if hasattr(msg, 'content'):
                                chat_history.append(msg)

                        logger.info(f"📝 Retrieved {len(chat_history)} messages from production memory")
                    else:
                        logger.info("📝 No messages found in production memory")
                except Exception as e:
                    logger.warning(f"Could not retrieve chat history from production memory: {e}")

            # Fallback to MongoDB checkpoints if production memory fails
            if not chat_history and self.memory:
                try:
                    existing_checkpoint = self.memory.get(config)
                    if existing_checkpoint and hasattr(existing_checkpoint, 'channel_values'):
                        messages = existing_checkpoint.channel_values.get('messages', [])
                        chat_history = messages
                        logger.info(f"📝 Fallback: Retrieved {len(chat_history)} messages from MongoDB checkpoints")
                except Exception as e:
                    logger.warning(f"Could not retrieve chat history from MongoDB: {e}")

            # Get fresh user context for this conversation
            current_user_context = self._get_personalized_context()

            # Create dynamic prompt with current user context
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", MAIN_AGENT_PROMPT.format(personalized_context=current_user_context)),
                ("placeholder", "{chat_history}"),
                ("human", "{input}"),
                ("placeholder", "{agent_scratchpad}"),
            ])

            # Use the persistent agent with memory instead of creating fresh executor
            # Update the agent's prompt with current context
            from langchain.agents import create_tool_calling_agent
            self.agent.agent.runnable = create_tool_calling_agent(self.llm, self.tools, dynamic_prompt)

            # Invoke the agent with memory configuration
            try:
                response = self.agent.invoke({
                    "input": message,
                    "chat_history": chat_history
                }, config=config)
            except Exception as e:
                # Handle tool call history issues
                if "tool_calls that do not have a corresponding ToolMessage" in str(e):
                    logger.warning("⚠️ Tool call history issue detected, clearing conversation history")
                    # Clear the conversation history for this thread to start fresh
                    try:
                        # Clear from MongoDB memory
                        if hasattr(self.current_user, 'db'):
                            checkpoints_collection = self.current_user.db["checkpoints"]
                            checkpoint_writes_collection = self.current_user.db["checkpoint_writes"]

                            checkpoints_collection.delete_many({"thread_id": thread_id})
                            checkpoint_writes_collection.delete_many({"thread_id": thread_id})

                            logger.info("✅ Cleared conversation history, retrying...")

                            # Retry the request with clean history using persistent agent
                            response = self.agent.invoke({
                                "input": message,
                                "chat_history": []
                            }, config=config)
                        else:
                            raise e
                    except Exception as retry_error:
                        logger.error(f"Failed to recover from tool call issue: {retry_error}")
                        raise e
                else:
                    raise e

            # Extract the final response from AgentExecutor
            final_response = response.get("output", "I apologize, but I couldn't process your request properly.")

            # Store AI response in chat history
            if hasattr(self, 'production_memory') and self.production_memory:
                chat_history = self.production_memory.get_chat_history(user_id)
                chat_history.add_ai_message(final_response)

            # Memory persistence is handled automatically by AgentExecutor with MongoDB checkpointer

            # Check for pending bookings and add reminder if needed (with timeout protection)
            try:
                final_response = self._add_booking_reminder_if_needed(final_response, message, thread_id)
            except Exception as e:
                logger.warning(f"⚠️ Booking reminder check failed: {e}")
                # Continue without reminder

            log_agent_response(final_response)

            # Extract tool usage information from AgentExecutor intermediate steps
            tools_used = []
            try:
                if "intermediate_steps" in response:
                    for step in response["intermediate_steps"]:
                        if len(step) >= 2:
                            action, observation = step[0], step[1]
                            tool_name = getattr(action, 'tool', 'unknown')
                            tool_input = getattr(action, 'tool_input', {})

                            # Truncate long outputs for display
                            tool_output = str(observation)
                            if len(tool_output) > 200:
                                tool_output = tool_output[:200] + "..."

                            tools_used.append({
                                'name': tool_name,
                                'description': self._get_tool_description(tool_name),
                                'input': tool_input,
                                'output': tool_output
                            })
            except Exception as e:
                logger.warning(f"⚠️ Tool extraction failed: {e}")
                tools_used = []

            # Skip detailed logging to prevent hanging
            # TODO: Fix log_chat_exchange hanging issue
            logger.debug(f"Chat exchange completed: {len(tools_used)} tools used")

            return {
                "response": final_response,
                "tools_used": tools_used
            }

        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)

            # Provide user-friendly error messages
            if "contents is not specified" in str(e) or "empty content" in str(e):
                user_response = "I didn't receive a valid message. Please tell me how I can help you today!"
            elif "400" in str(e) and "Gemini" in str(e):
                user_response = "I'm having trouble processing your request. Please try rephrasing your message."
            elif "timeout" in str(e).lower():
                user_response = "The request is taking too long. Please try again with a shorter message."
            else:
                user_response = "I'm experiencing technical difficulties. Please try again in a moment."

            return {
                "response": user_response,
                "tools_used": []
            }

    async def chat_stream(self, message: str, thread_id: str = None):
        """
        Process a user message and stream the response using LangChain streaming

        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management (uses permanent session if None)

        Yields:
            Dict chunks with streaming response and tools_used
        """
        user_id = str(self.current_user.user.id)

        # Use permanent session if no thread_id provided
        if thread_id is None:
            if hasattr(self, 'production_memory') and self.production_memory:
                session = self.production_memory.get_permanent_session(user_id)
                thread_id = session.thread_id
            else:
                thread_id = f"permanent_{user_id}"

        if not self.agent:
            raise ValueError("Agent not initialized. Call set_tools() first.")

        # Validate message content
        if not message or not message.strip():
            yield {
                "type": "complete",
                "response": "I didn't receive any message. Please tell me how I can help you today!",
                "tools_used": []
            }
            return

        # Limit message length to prevent API issues
        if len(message) > 2000:
            yield {
                "type": "complete",
                "response": "Your message is too long. Please keep it under 2000 characters and try again.",
                "tools_used": []
            }
            return

        log_user_input(message)

        # Store message in chat history
        if hasattr(self, 'production_memory') and self.production_memory:
            chat_history = self.production_memory.get_chat_history(user_id)
            chat_history.add_user_message(message)

        # Configure thread for memory persistence with user_id for memory tools
        config = {
            "configurable": {
                "thread_id": thread_id,
                "user_id": user_id,
                "checkpoint_ns": ""  # Add checkpoint namespace for MongoDB saver
            }
        }

        try:
            # Get chat history from MongoDB memory for AgentExecutor
            chat_history = []
            if self.memory:
                try:
                    existing_checkpoint = self.memory.get(config)
                    if existing_checkpoint and hasattr(existing_checkpoint, 'channel_values'):
                        messages = existing_checkpoint.channel_values.get('messages', [])
                        # Convert to chat history format for AgentExecutor
                        chat_history = messages
                except Exception as e:
                    logger.warning(f"Could not retrieve chat history: {e}")

            # Get fresh user context for streaming
            current_user_context = self._get_personalized_context()

            # Create dynamic prompt with current user context for streaming
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", MAIN_AGENT_PROMPT.format(personalized_context=current_user_context)),
                ("placeholder", "{chat_history}"),
                ("human", "{input}"),
                ("placeholder", "{agent_scratchpad}"),
            ])

            # Create fresh executor for streaming with current context
            from langchain.agents import create_tool_calling_agent, AgentExecutor
            fresh_agent = create_tool_calling_agent(self.llm, self.tools, dynamic_prompt)
            fresh_executor = AgentExecutor(
                agent=fresh_agent,
                tools=self.tools,
                verbose=False,
                handle_parsing_errors=True,
                max_iterations=3
            )

            # Stream the agent response using astream
            full_response = ""
            tools_used = []

            try:
                async for chunk in fresh_executor.astream({
                    "input": message,
                    "chat_history": chat_history
                }, config=config):

                    # Handle different chunk types from AgentExecutor
                    if "agent" in chunk:
                        # Agent is thinking/planning
                        agent_chunk = chunk["agent"]
                        if hasattr(agent_chunk, 'content') and agent_chunk.content:
                            # Stream the agent's response content
                            yield {
                                "type": "chunk",
                                "content": agent_chunk.content,
                                "full_response": full_response + agent_chunk.content
                            }
                            full_response += agent_chunk.content

                    elif "tools" in chunk:
                        # Tool execution
                        tool_chunk = chunk["tools"]
                        if isinstance(tool_chunk, list):
                            for tool_result in tool_chunk:
                                if hasattr(tool_result, 'name'):
                                    tool_name = tool_result.name
                                    tool_output = str(tool_result.content) if hasattr(tool_result, 'content') else str(tool_result)

                                    # Truncate long outputs for display
                                    if len(tool_output) > 200:
                                        tool_output = tool_output[:200] + "..."

                                    tools_used.append({
                                        'name': tool_name,
                                        'description': self._get_tool_description(tool_name),
                                        'input': {},
                                        'output': tool_output
                                    })

                                    # Yield tool usage info
                                    yield {
                                        "type": "tool",
                                        "tool_name": tool_name,
                                        "tool_output": tool_output
                                    }

                    elif "output" in chunk:
                        # Final output from agent
                        output_chunk = chunk["output"]
                        if isinstance(output_chunk, str):
                            # Stream the final response
                            yield {
                                "type": "chunk",
                                "content": output_chunk,
                                "full_response": output_chunk
                            }
                            full_response = output_chunk
                        elif isinstance(output_chunk, dict) and "output" in output_chunk:
                            final_output = output_chunk["output"]
                            yield {
                                "type": "chunk",
                                "content": final_output,
                                "full_response": final_output
                            }
                            full_response = final_output

            except Exception as e:
                # Handle tool call history issues
                if "tool_calls that do not have a corresponding ToolMessage" in str(e):
                    logger.warning("⚠️ Tool call history issue detected, clearing conversation history")
                    # Clear the conversation history for this thread to start fresh
                    try:
                        # Clear from MongoDB memory
                        if hasattr(self.current_user, 'db'):
                            checkpoints_collection = self.current_user.db["checkpoints"]
                            checkpoint_writes_collection = self.current_user.db["checkpoint_writes"]

                            checkpoints_collection.delete_many({"thread_id": thread_id})
                            checkpoint_writes_collection.delete_many({"thread_id": thread_id})

                            logger.info("✅ Cleared conversation history, retrying...")

                            # Retry the request with clean history
                            async for chunk in self.agent.astream({
                                "input": message,
                                "chat_history": []
                            }, config=config):

                                if "agent" in chunk:
                                    agent_chunk = chunk["agent"]
                                    if hasattr(agent_chunk, 'content') and agent_chunk.content:
                                        yield {
                                            "type": "chunk",
                                            "content": agent_chunk.content,
                                            "full_response": full_response + agent_chunk.content
                                        }
                                        full_response += agent_chunk.content

                                elif "output" in chunk:
                                    output_chunk = chunk["output"]
                                    if isinstance(output_chunk, str):
                                        yield {
                                            "type": "chunk",
                                            "content": output_chunk,
                                            "full_response": output_chunk
                                        }
                                        full_response = output_chunk
                        else:
                            raise e
                    except Exception as retry_error:
                        logger.error(f"Failed to recover from tool call issue: {retry_error}")
                        raise e
                else:
                    raise e

            # Store AI response in chat history
            if hasattr(self, 'production_memory') and self.production_memory:
                chat_history = self.production_memory.get_chat_history(user_id)
                chat_history.add_ai_message(full_response)

            # Memory persistence is handled automatically by AgentExecutor with MongoDB checkpointer

            # Check for pending bookings and add reminder if needed
            final_response = self._add_booking_reminder_if_needed(full_response, message, thread_id)
            if final_response != full_response:
                # Stream the booking reminder
                reminder_text = final_response[len(full_response):]
                yield {
                    "type": "chunk",
                    "content": reminder_text,
                    "full_response": final_response
                }
                full_response = final_response

            log_agent_response(full_response)

            # Send completion signal
            yield {
                "type": "complete",
                "response": full_response,
                "tools_used": tools_used
            }

        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)

            # Provide user-friendly error messages
            if "contents is not specified" in str(e) or "empty content" in str(e):
                user_response = "I didn't receive a valid message. Please tell me how I can help you today!"
            elif "400" in str(e) and "Gemini" in str(e):
                user_response = "I'm having trouble processing your request. Please try rephrasing your message."
            elif "timeout" in str(e).lower():
                user_response = "The request is taking too long. Please try again with a shorter message."
            else:
                user_response = "I'm experiencing technical difficulties. Please try again in a moment."

            yield {
                "type": "error",
                "response": user_response,
                "tools_used": []
            }
