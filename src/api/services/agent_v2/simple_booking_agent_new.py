"""
Simplified Booking Agent - No LLM calls, delegates everything to main agent
"""

import logging
from models.user import UserTenantDB

logger = logging.getLogger(__name__)


class SimpleBookingAgent:
    """
    Simplified Booking Agent - Just returns basic responses, main agent handles all logic
    """
    
    def __init__(self, current_user: UserTenantDB = None):
        """Initialize the booking agent"""
        self.current_user = current_user
        logger.info("✅ Simple Booking Agent initialized")

    def handle_booking_request(self, user_message: str, thread_id: str) -> str:
        """Handle booking request - simplified to just return a basic response"""
        try:
            logger.info(f"📅 Booking request: {user_message}")
            
            # Simple response - let main agent handle all the logic
            return "I'll help you with booking. Let me get the course information for you."
            
        except Exception as e:
            logger.error(f"Error handling booking: {str(e)}")
            return "I'll help you with booking. Let me get the course information for you."

    def get_pending_booking_reminder(self, _thread_id: str) -> str:
        """Get reminder message for pending booking"""
        return ""
