#!/usr/bin/env python3
"""
Test script to verify the infinite loop issue is fixed
Tests that agent properly handles confirmations and proceeds with booking
"""

import requests
import json
import time

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "admin"
AUTH_TOKEN = "test-token"

def make_chat_request(message, user_id=TEST_USER_ID):
    """Make a chat request"""
    url = f"{BASE_URL}/api/v1/chat"
    
    payload = {
        "message": message,
        "user_id": user_id
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=None)
        
        if response.status_code == 200:
            result = response.json()
            return {
                "success": True,
                "response": result.get('response', ''),
                "tools_used": result.get('tools_used', [])
            }
        else:
            return {
                "success": False,
                "error": f"Status {response.status_code}: {response.text}"
            }
            
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def test_loop_fix():
    """Test that the infinite loop is fixed"""
    print("🧪 Testing Infinite Loop Fix")
    print("=" * 50)
    
    # Step 1: Clear any existing data
    print("\n🗑️ Step 1: Clearing existing data...")
    try:
        clear_url = f"{BASE_URL}/api/v1/chat/clear"
        headers = {"Authorization": f"Bearer {AUTH_TOKEN}"}
        clear_response = requests.delete(clear_url, headers=headers)
        if clear_response.status_code == 200:
            print("  ✅ Data cleared successfully")
        else:
            print(f"  ⚠️ Clear failed: {clear_response.status_code}")
    except Exception as e:
        print(f"  ⚠️ Clear error: {e}")
    
    time.sleep(2)  # Wait for cleanup
    
    # Step 2: Create an incomplete booking scenario
    print("\n📝 Step 2: Creating incomplete booking scenario...")
    
    # Send a booking interest message
    result1 = make_chat_request("I want to book SEE Bridge course")
    if result1["success"]:
        print(f"  ✅ Initial booking interest: {result1['response'][:100]}...")
        tools_used = result1.get('tools_used', [])
        if tools_used:
            print(f"  🛠️ Tools used: {', '.join(tools_used)}")
    else:
        print(f"  ❌ Failed: {result1['error']}")
        return
    
    time.sleep(2)
    
    # Step 3: Test greeting (should detect incomplete process)
    print("\n👋 Step 3: Testing greeting with incomplete process...")
    
    result2 = make_chat_request("Hi")
    if result2["success"]:
        response_text = result2['response'].lower()
        print(f"  🤖 Greeting response: {result2['response'][:150]}...")
        
        # Check if it mentions the incomplete booking
        if "see bridge" in response_text or "booking" in response_text:
            print("  ✅ Correctly detected incomplete booking process")
        else:
            print("  ❌ Did not detect incomplete booking process")
    else:
        print(f"  ❌ Failed: {result2['error']}")
        return
    
    time.sleep(2)
    
    # Step 4: Test confirmation (should proceed with booking, not loop)
    print("\n✅ Step 4: Testing confirmation - should proceed with booking...")
    
    result3 = make_chat_request("yes, let's continue with the booking")
    if result3["success"]:
        response_text = result3['response'].lower()
        tools_used = result3.get('tools_used', [])
        
        print(f"  🤖 Confirmation response: {result3['response'][:150]}...")
        print(f"  🛠️ Tools used: {', '.join(tools_used) if tools_used else 'None'}")
        
        # Check if it used the booking tool
        if 'handle_booking' in tools_used:
            print("  ✅ SUCCESS: Used booking tool after confirmation")
        else:
            print("  ❌ FAILED: Did not use booking tool after confirmation")
        
        # Check if it's asking the same question again (loop behavior)
        if "would you like to continue" in response_text and "booking" in response_text:
            print("  ❌ FAILED: Still asking about continuing booking (loop detected)")
        else:
            print("  ✅ SUCCESS: Not stuck in loop, proceeding with booking")
            
    else:
        print(f"  ❌ Failed: {result3['error']}")
        return
    
    time.sleep(2)
    
    # Step 5: Test Nepali confirmation
    print("\n🌐 Step 5: Testing Nepali confirmation...")
    
    # First create another incomplete scenario
    make_chat_request("I'm interested in IELTS course")
    time.sleep(1)
    make_chat_request("Hi")  # Should detect incomplete process
    time.sleep(1)
    
    result4 = make_chat_request("hajur")  # Nepali "yes"
    if result4["success"]:
        response_text = result4['response'].lower()
        tools_used = result4.get('tools_used', [])
        
        print(f"  🤖 Nepali confirmation response: {result4['response'][:150]}...")
        print(f"  🛠️ Tools used: {', '.join(tools_used) if tools_used else 'None'}")
        
        # Check if it understood Nepali confirmation
        if 'handle_booking' in tools_used:
            print("  ✅ SUCCESS: Understood Nepali confirmation and used booking tool")
        else:
            print("  ❌ FAILED: Did not understand Nepali confirmation")
            
    else:
        print(f"  ❌ Failed: {result4['error']}")
    
    print("\n" + "=" * 50)
    print("🔍 Test Summary:")
    print("  ✓ Incomplete process detection")
    print("  ✓ English confirmation handling")
    print("  ✓ Nepali confirmation handling")
    print("  ✓ Loop prevention")
    print("  ✓ Proper tool usage after confirmation")

if __name__ == "__main__":
    test_loop_fix()
