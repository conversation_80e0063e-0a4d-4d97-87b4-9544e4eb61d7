#!/usr/bin/env python3
"""
Test script for comprehensive user data deletion
Tests that all user data is properly cleared when conversation is deleted
"""

import requests
import json
import time

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_USER_ID = "admin"
AUTH_TOKEN = "test-token"

def make_request(method, endpoint, data=None):
    """Make authenticated request to API"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    url = f"{BASE_URL}{endpoint}"
    
    if method.upper() == "POST":
        response = requests.post(url, json=data, headers=headers)
    elif method.upper() == "DELETE":
        response = requests.delete(url, headers=headers)
    elif method.upper() == "GET":
        response = requests.get(url, headers=headers)
    else:
        raise ValueError(f"Unsupported method: {method}")
    
    return response

def test_comprehensive_deletion():
    """Test comprehensive user data deletion"""
    print("🧪 Testing Comprehensive User Data Deletion")
    print("=" * 60)
    
    # Step 1: Send some messages to create data
    print("\n📝 Step 1: Creating user data...")
    
    test_messages = [
        "नमस्ते, मेरो नाम Diwas हो",
        "I want to learn SEE Bridge course",
        "hajur, malai booking garna parne xa"
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"  Sending message {i}: {message}")
        response = make_request("POST", "/api/v1/chat", {"message": message, "user_id": TEST_USER_ID})
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Response: {result.get('response', '')[:50]}...")
        else:
            print(f"  ❌ Failed: {response.status_code} - {response.text}")
        
        time.sleep(1)  # Small delay between messages
    
    # Step 2: Check that data exists
    print("\n🔍 Step 2: Verifying data exists...")
    
    # Check chat history
    history_response = make_request("GET", "/api/v1/chat-history/history")
    if history_response.status_code == 200:
        history_data = history_response.json()
        message_count = len(history_data.get('messages', []))
        print(f"  📊 Chat history: {message_count} messages found")
    else:
        print(f"  ❌ Failed to get chat history: {history_response.status_code}")
    
    # Step 3: Perform comprehensive deletion
    print("\n🗑️  Step 3: Performing comprehensive deletion...")
    
    delete_response = make_request("DELETE", "/api/v1/chat/clear")
    
    if delete_response.status_code == 200:
        delete_result = delete_response.json()
        print(f"  ✅ Deletion successful!")
        print(f"  📊 Deletion summary:")
        
        summary = delete_result.get('deletion_summary', {})
        for key, value in summary.items():
            if isinstance(value, int) and value > 0:
                print(f"    - {key.replace('_', ' ').title()}: {value}")
            elif isinstance(value, bool) and value:
                print(f"    - {key.replace('_', ' ').title()}: Yes")
        
        errors = delete_result.get('errors')
        if errors:
            print(f"  ⚠️  Errors encountered: {errors}")
    else:
        print(f"  ❌ Deletion failed: {delete_response.status_code} - {delete_response.text}")
        return False
    
    # Step 4: Verify data is completely gone
    print("\n🔍 Step 4: Verifying all data is deleted...")
    
    # Check chat history again
    history_response = make_request("GET", "/api/v1/chat-history/history")
    if history_response.status_code == 200:
        history_data = history_response.json()
        message_count = len(history_data.get('messages', []))
        if message_count == 0:
            print(f"  ✅ Chat history cleared: {message_count} messages")
        else:
            print(f"  ❌ Chat history not cleared: {message_count} messages still exist")
    else:
        print(f"  ❌ Failed to verify chat history: {history_response.status_code}")
    
    # Step 5: Test that user is treated as new
    print("\n🆕 Step 5: Testing fresh user experience...")
    
    fresh_response = make_request("POST", "/api/v1/chat", {"message": "नमस्ते", "user_id": TEST_USER_ID})
    
    if fresh_response.status_code == 200:
        fresh_result = fresh_response.json()
        response_text = fresh_result.get('response', '').lower()
        
        # Check if the response treats user as new (shouldn't remember name or previous context)
        if "diwas" not in response_text and "see bridge" not in response_text:
            print(f"  ✅ User treated as new: No previous context remembered")
            print(f"  🤖 Fresh response: {fresh_result.get('response', '')[:100]}...")
        else:
            print(f"  ❌ User context still remembered!")
            print(f"  🤖 Response: {fresh_result.get('response', '')}")
    else:
        print(f"  ❌ Failed to test fresh experience: {fresh_response.status_code}")
    
    print("\n" + "=" * 60)
    print("✅ Comprehensive deletion test completed!")
    print("\n🔍 Key Features Tested:")
    print("  ✓ Chat message deletion")
    print("  ✓ Structured memory deletion")
    print("  ✓ User profile deletion")
    print("  ✓ Session data deletion")
    print("  ✓ MongoDB checkpoint deletion")
    print("  ✓ Fresh user experience verification")

if __name__ == "__main__":
    test_comprehensive_deletion()
